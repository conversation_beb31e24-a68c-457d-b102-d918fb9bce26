"""
Main script to run the complete RAG Quote Retrieval System pipeline
"""

import os
import sys
import logging
import argparse
from typing import Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_data_preparation():
    """Run data preparation step"""
    logger.info("Starting data preparation...")
    try:
        from data_preparation import QuoteDataProcessor
        
        processor = QuoteDataProcessor()
        
        # Check if processed data already exists
        try:
            df = processor.load_processed_data()
            logger.info("Processed data already exists, skipping data preparation")
            return df
        except FileNotFoundError:
            pass
        
        # Download and process data
        raw_df = processor.download_dataset()
        processed_df = processor.preprocess_data(raw_df)
        
        # Get and display statistics
        stats = processor.get_data_statistics(processed_df)
        logger.info("Dataset Statistics:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # Save processed data
        processor.save_processed_data(processed_df)
        
        logger.info("Data preparation completed successfully!")
        return processed_df
        
    except Exception as e:
        logger.error(f"Error in data preparation: {e}")
        raise

def run_model_training(skip_if_exists: bool = True):
    """Run model training step"""
    logger.info("Starting model training...")
    
    try:
        from model_training import QuoteEmbeddingTrainer
        from data_preparation import QuoteDataProcessor
        
        # Check if model already exists
        model_path = "./models/fine_tuned_quotes_model"
        if skip_if_exists and os.path.exists(model_path):
            logger.info("Fine-tuned model already exists, skipping training")
            return
        
        # Load processed data
        processor = QuoteDataProcessor()
        df = processor.load_processed_data()
        
        # Initialize trainer and train model
        trainer = QuoteEmbeddingTrainer()
        trainer.fine_tune_model(df, epochs=2, batch_size=16)
        
        # Evaluate model
        trainer.evaluate_model(df)
        
        # Save model
        trainer.save_model()
        
        logger.info("Model training completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in model training: {e}")
        raise

def run_rag_pipeline_setup():
    """Setup and test RAG pipeline"""
    logger.info("Setting up RAG pipeline...")
    
    try:
        from rag_pipeline import QuoteRAGPipeline
        from data_preparation import QuoteDataProcessor
        
        # Load processed data
        processor = QuoteDataProcessor()
        df = processor.load_processed_data()
        
        # Initialize RAG pipeline
        rag = QuoteRAGPipeline()
        
        # Index quotes if not already done
        if rag.collection.count() == 0:
            logger.info("Indexing quotes in vector database...")
            rag.index_quotes(df)
        else:
            logger.info(f"Vector database already contains {rag.collection.count()} documents")
        
        # Test with sample queries
        test_queries = [
            "Quotes about hope",
            "Oscar Wilde quotes",
            "Motivational quotes about success"
        ]
        
        logger.info("Testing RAG pipeline with sample queries...")
        for query in test_queries:
            logger.info(f"Testing query: '{query}'")
            result = rag.search(query, top_k=3)
            logger.info(f"Retrieved {result['num_results']} quotes")
        
        logger.info("RAG pipeline setup completed successfully!")
        return rag
        
    except Exception as e:
        logger.error(f"Error in RAG pipeline setup: {e}")
        raise

def run_evaluation():
    """Run evaluation of the RAG system"""
    logger.info("Starting evaluation...")
    
    try:
        from evaluation import QuoteRAGEvaluator
        from rag_pipeline import QuoteRAGPipeline
        from data_preparation import QuoteDataProcessor
        
        # Load processed data
        processor = QuoteDataProcessor()
        df = processor.load_processed_data()
        
        # Initialize RAG pipeline
        rag = QuoteRAGPipeline()
        
        # Ensure database is indexed
        if rag.collection.count() == 0:
            logger.info("Indexing quotes for evaluation...")
            rag.index_quotes(df)
        
        # Initialize evaluator
        evaluator = QuoteRAGEvaluator(rag)
        
        # Run comprehensive evaluation
        results = evaluator.run_comprehensive_evaluation(df, num_samples=30)
        
        # Print and save results
        evaluator.print_evaluation_summary()
        evaluator.save_evaluation_results()
        
        logger.info("Evaluation completed successfully!")
        return results
        
    except Exception as e:
        logger.error(f"Error in evaluation: {e}")
        raise

def run_streamlit_app():
    """Launch the Streamlit application"""
    logger.info("Launching Streamlit application...")
    
    try:
        import subprocess
        import sys
        
        # Run streamlit app
        cmd = [sys.executable, "-m", "streamlit", "run", "app.py"]
        subprocess.run(cmd)
        
    except Exception as e:
        logger.error(f"Error launching Streamlit app: {e}")
        raise

def main():
    """Main function to orchestrate the entire pipeline"""
    parser = argparse.ArgumentParser(description="RAG Quote Retrieval System")
    parser.add_argument("--step", choices=["all", "data", "train", "rag", "eval", "app"], 
                       default="all", help="Which step to run")
    parser.add_argument("--skip-training", action="store_true", 
                       help="Skip model training if model exists")
    parser.add_argument("--no-eval", action="store_true", 
                       help="Skip evaluation step")
    
    args = parser.parse_args()
    
    logger.info("Starting RAG Quote Retrieval System Pipeline")
    logger.info(f"Running step: {args.step}")
    
    try:
        if args.step in ["all", "data"]:
            run_data_preparation()
        
        if args.step in ["all", "train"]:
            run_model_training(skip_if_exists=args.skip_training)
        
        if args.step in ["all", "rag"]:
            run_rag_pipeline_setup()
        
        if args.step in ["all", "eval"] and not args.no_eval:
            run_evaluation()
        
        if args.step in ["all", "app"]:
            logger.info("Pipeline setup completed successfully!")
            logger.info("Launching Streamlit application...")
            run_streamlit_app()
        
        if args.step == "all":
            logger.info("="*50)
            logger.info("PIPELINE COMPLETED SUCCESSFULLY!")
            logger.info("="*50)
            logger.info("You can now:")
            logger.info("1. Run 'streamlit run app.py' to launch the web interface")
            logger.info("2. Use the RAG pipeline programmatically")
            logger.info("3. Check evaluation_results.json for performance metrics")
    
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
