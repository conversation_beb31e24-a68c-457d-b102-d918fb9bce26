"""
Model Training Module for Quote Retrieval System
Fine-tunes a sentence embedding model on the quotes dataset
"""

import pandas as pd
import numpy as np
import torch
from sentence_transformers import SentenceTransformer, InputExample, losses
from sentence_transformers.evaluation import EmbeddingSimilarityEvaluator
from torch.utils.data import DataLoader
import os
import random
from typing import List, Tuple
import logging
from sklearn.model_selection import train_test_split
import pickle

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QuoteEmbeddingTrainer:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2", output_dir: str = "./models"):
        """
        Initialize the embedding trainer
        
        Args:
            model_name: Base model to fine-tune
            output_dir: Directory to save the fine-tuned model
        """
        self.model_name = model_name
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Load base model
        logger.info(f"Loading base model: {model_name}")
        self.model = SentenceTransformer(model_name)
        
    def create_training_examples(self, df: pd.DataFrame, num_examples: int = 10000) -> List[InputExample]:
        """
        Create training examples for fine-tuning
        
        Args:
            df: Processed quotes DataFrame
            num_examples: Number of training examples to create
            
        Returns:
            List of InputExample objects
        """
        logger.info("Creating training examples...")
        
        examples = []
        
        # Create positive pairs (quote with its metadata)
        for idx, row in df.iterrows():
            quote = row['quote_cleaned']
            author = row['author_cleaned']
            tags = row['tags_str']
            
            if len(quote) < 10:  # Skip very short quotes
                continue
                
            # Positive examples: quote with author/tags
            if author and author != "Unknown":
                examples.append(InputExample(texts=[quote, f"Author: {author}"], label=0.8))
                examples.append(InputExample(texts=[f"Quote by {author}", quote], label=0.8))
            
            if tags:
                examples.append(InputExample(texts=[quote, f"Tags: {tags}"], label=0.7))
                examples.append(InputExample(texts=[f"Quote about {tags}", quote], label=0.7))
            
            # Combined context
            combined = row['combined_text']
            examples.append(InputExample(texts=[quote, combined], label=0.9))
            
            if len(examples) >= num_examples:
                break
        
        # Create negative pairs (random quotes)
        df_sample = df.sample(min(1000, len(df)))
        quotes = df_sample['quote_cleaned'].tolist()
        
        for _ in range(min(2000, num_examples // 5)):
            quote1, quote2 = random.sample(quotes, 2)
            if quote1 != quote2:
                examples.append(InputExample(texts=[quote1, quote2], label=0.1))
        
        # Shuffle examples
        random.shuffle(examples)
        
        logger.info(f"Created {len(examples)} training examples")
        return examples[:num_examples]
    
    def create_evaluation_examples(self, df: pd.DataFrame, num_examples: int = 1000) -> List[InputExample]:
        """
        Create evaluation examples
        
        Args:
            df: Processed quotes DataFrame
            num_examples: Number of evaluation examples
            
        Returns:
            List of InputExample objects for evaluation
        """
        logger.info("Creating evaluation examples...")
        
        examples = []
        df_sample = df.sample(min(num_examples, len(df)))
        
        for idx, row in df_sample.iterrows():
            quote = row['quote_cleaned']
            author = row['author_cleaned']
            
            if len(quote) < 10 or not author or author == "Unknown":
                continue
                
            # Create query-quote pairs
            examples.append(InputExample(texts=[f"Quote by {author}", quote], label=0.8))
            examples.append(InputExample(texts=[quote, f"Author: {author}"], label=0.8))
        
        logger.info(f"Created {len(examples)} evaluation examples")
        return examples
    
    def fine_tune_model(self, df: pd.DataFrame, epochs: int = 3, batch_size: int = 16):
        """
        Fine-tune the sentence transformer model
        
        Args:
            df: Processed quotes DataFrame
            epochs: Number of training epochs
            batch_size: Training batch size
        """
        logger.info("Starting model fine-tuning...")
        
        # Create training and evaluation examples
        train_examples = self.create_training_examples(df)
        eval_examples = self.create_evaluation_examples(df)
        
        # Split training examples
        train_examples, val_examples = train_test_split(train_examples, test_size=0.1, random_state=42)
        
        # Create data loaders
        train_dataloader = DataLoader(train_examples, shuffle=True, batch_size=batch_size)
        
        # Define loss function
        train_loss = losses.CosineSimilarityLoss(self.model)
        
        # Create evaluator
        evaluator = EmbeddingSimilarityEvaluator.from_input_examples(
            eval_examples, name='quotes-eval'
        )
        
        # Training arguments
        warmup_steps = int(len(train_dataloader) * epochs * 0.1)
        
        logger.info(f"Training with {len(train_examples)} examples for {epochs} epochs")
        
        # Fine-tune the model
        self.model.fit(
            train_objectives=[(train_dataloader, train_loss)],
            epochs=epochs,
            warmup_steps=warmup_steps,
            evaluator=evaluator,
            evaluation_steps=500,
            output_path=os.path.join(self.output_dir, "fine_tuned_quotes_model"),
            save_best_model=True,
            show_progress_bar=True
        )
        
        logger.info("Model fine-tuning completed!")
    
    def save_model(self, model_path: str = None):
        """
        Save the fine-tuned model
        
        Args:
            model_path: Path to save the model
        """
        if model_path is None:
            model_path = os.path.join(self.output_dir, "fine_tuned_quotes_model")
        
        self.model.save(model_path)
        logger.info(f"Model saved to {model_path}")
    
    def load_model(self, model_path: str = None) -> SentenceTransformer:
        """
        Load a fine-tuned model
        
        Args:
            model_path: Path to the saved model
            
        Returns:
            Loaded SentenceTransformer model
        """
        if model_path is None:
            model_path = os.path.join(self.output_dir, "fine_tuned_quotes_model")
        
        if os.path.exists(model_path):
            self.model = SentenceTransformer(model_path)
            logger.info(f"Model loaded from {model_path}")
            return self.model
        else:
            logger.warning(f"Model not found at {model_path}, using base model")
            return self.model
    
    def evaluate_model(self, df: pd.DataFrame, num_samples: int = 100):
        """
        Evaluate the fine-tuned model
        
        Args:
            df: Processed quotes DataFrame
            num_samples: Number of samples for evaluation
        """
        logger.info("Evaluating model...")
        
        # Sample quotes for evaluation
        sample_df = df.sample(min(num_samples, len(df)))
        
        # Test semantic similarity
        quotes = sample_df['quote_cleaned'].tolist()
        authors = sample_df['author_cleaned'].tolist()
        
        # Encode quotes and author queries
        quote_embeddings = self.model.encode(quotes)
        author_queries = [f"Quote by {author}" for author in authors]
        author_embeddings = self.model.encode(author_queries)
        
        # Calculate similarities
        similarities = []
        for i in range(len(quotes)):
            if authors[i] != "Unknown":
                sim = np.dot(quote_embeddings[i], author_embeddings[i])
                similarities.append(sim)
        
        avg_similarity = np.mean(similarities) if similarities else 0
        logger.info(f"Average quote-author similarity: {avg_similarity:.4f}")
        
        return avg_similarity

def main():
    """Main function to run model training"""
    from data_preparation import QuoteDataProcessor
    
    # Load processed data
    processor = QuoteDataProcessor()
    
    try:
        df = processor.load_processed_data()
    except FileNotFoundError:
        logger.info("Processed data not found, running data preparation...")
        raw_df = processor.download_dataset()
        df = processor.preprocess_data(raw_df)
        processor.save_processed_data(df)
    
    # Initialize trainer
    trainer = QuoteEmbeddingTrainer()
    
    # Fine-tune model
    trainer.fine_tune_model(df, epochs=2, batch_size=16)
    
    # Evaluate model
    trainer.evaluate_model(df)
    
    # Save model
    trainer.save_model()
    
    logger.info("Model training completed successfully!")

if __name__ == "__main__":
    main()
