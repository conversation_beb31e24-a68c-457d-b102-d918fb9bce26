"""
Streamlit Application for Quote Retrieval System
Interactive web interface for semantic quote search
"""

import streamlit as st
import pandas as pd
import json
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any
import logging
import os
import time

# Import our modules
from rag_pipeline import QuoteRAGPipeline
from data_preparation import QuoteDataProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Quote Retrieval System",
    page_icon="📚",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .quote-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin: 1rem 0;
    }
    .similarity-score {
        background-color: #e3f2fd;
        padding: 0.2rem 0.5rem;
        border-radius: 0.3rem;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def load_rag_pipeline():
    """Load and cache the RAG pipeline"""
    try:
        rag = QuoteRAGPipeline()
        return rag
    except Exception as e:
        st.error(f"Error loading RAG pipeline: {e}")
        return None

@st.cache_data
def load_dataset_info():
    """Load and cache dataset information"""
    try:
        processor = QuoteDataProcessor()
        df = processor.load_processed_data()
        stats = processor.get_data_statistics(df)
        return df, stats
    except Exception as e:
        st.error(f"Error loading dataset: {e}")
        return None, None

def initialize_database(rag_pipeline, df):
    """Initialize the vector database if needed"""
    if rag_pipeline.collection.count() == 0:
        with st.spinner("Initializing vector database... This may take a few minutes."):
            rag_pipeline.index_quotes(df)
        st.success("Vector database initialized successfully!")
    return True

def display_quote_card(quote_data: Dict, index: int):
    """Display a quote in a card format"""
    with st.container():
        st.markdown(f"""
        <div class="quote-card">
            <h4>Quote {index}</h4>
            <blockquote>"{quote_data['quote']}"</blockquote>
            <p><strong>Author:</strong> {quote_data['author']}</p>
            <p><strong>Tags:</strong> {quote_data['tags'] if quote_data['tags'] else 'None'}</p>
            <p><span class="similarity-score">Similarity: {quote_data['similarity_score']:.3f}</span></p>
        </div>
        """, unsafe_allow_html=True)

def create_similarity_chart(retrieved_quotes: List[Dict]):
    """Create a similarity score chart"""
    if not retrieved_quotes:
        return None
    
    authors = [q['author'][:20] + "..." if len(q['author']) > 20 else q['author'] for q in retrieved_quotes]
    scores = [q['similarity_score'] for q in retrieved_quotes]
    
    fig = px.bar(
        x=scores,
        y=authors,
        orientation='h',
        title="Similarity Scores by Author",
        labels={'x': 'Similarity Score', 'y': 'Author'},
        color=scores,
        color_continuous_scale='Blues'
    )
    
    fig.update_layout(
        height=400,
        showlegend=False,
        yaxis={'categoryorder': 'total ascending'}
    )
    
    return fig

def create_tags_distribution(retrieved_quotes: List[Dict]):
    """Create a tags distribution chart"""
    if not retrieved_quotes:
        return None
    
    # Extract all tags
    all_tags = []
    for quote in retrieved_quotes:
        if quote['tags']:
            tags = [tag.strip() for tag in quote['tags'].split(',')]
            all_tags.extend(tags)
    
    if not all_tags:
        return None
    
    # Count tag frequencies
    tag_counts = {}
    for tag in all_tags:
        tag_counts[tag] = tag_counts.get(tag, 0) + 1
    
    # Create chart
    tags = list(tag_counts.keys())
    counts = list(tag_counts.values())
    
    fig = px.pie(
        values=counts,
        names=tags,
        title="Tag Distribution in Retrieved Quotes"
    )
    
    return fig

def export_results_to_json(query: str, results: Dict):
    """Export search results to JSON format"""
    export_data = {
        'query': query,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'response': results['response'],
        'retrieved_quotes': results['retrieved_quotes'],
        'num_results': results['num_results']
    }
    
    return json.dumps(export_data, indent=2)

def main():
    """Main Streamlit application"""
    
    # Header
    st.markdown('<h1 class="main-header">📚 Semantic Quote Retrieval System</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar
    with st.sidebar:
        st.header("🔧 Configuration")
        
        # Search parameters
        top_k = st.slider("Number of quotes to retrieve", min_value=1, max_value=20, value=5)
        
        # Display options
        st.subheader("Display Options")
        show_similarity_chart = st.checkbox("Show similarity chart", value=True)
        show_tags_chart = st.checkbox("Show tags distribution", value=True)
        show_raw_response = st.checkbox("Show raw LLM response", value=False)
        
        # Dataset info
        st.subheader("📊 Dataset Information")
        df, stats = load_dataset_info()
        
        if stats:
            st.metric("Total Quotes", stats['total_quotes'])
            st.metric("Unique Authors", stats['unique_authors'])
            st.metric("Unique Tags", stats['unique_tags'])
            st.metric("Avg Quote Length", f"{stats['avg_quote_length']:.0f} chars")
    
    # Main content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("🔍 Search Quotes")
        
        # Search interface
        query = st.text_input(
            "Enter your search query:",
            placeholder="e.g., 'Quotes about hope by Oscar Wilde' or 'Motivational quotes about success'",
            help="You can search by author, topic, tags, or content"
        )
        
        # Example queries
        st.subheader("💡 Example Queries")
        example_queries = [
            "Quotes about insanity attributed to Einstein",
            "Motivational quotes tagged 'accomplishment'",
            "All Oscar Wilde quotes with humor",
            "Quotes about love and life",
            "Inspirational quotes by women authors"
        ]
        
        selected_example = st.selectbox("Or select an example query:", [""] + example_queries)
        
        if selected_example:
            query = selected_example
            st.rerun()
    
    with col2:
        st.header("🚀 Quick Actions")
        
        # Load pipeline
        if st.button("🔄 Reload Pipeline", help="Reload the RAG pipeline"):
            st.cache_resource.clear()
            st.success("Pipeline reloaded!")
        
        # System status
        st.subheader("📈 System Status")
        rag_pipeline = load_rag_pipeline()
        
        if rag_pipeline:
            db_count = rag_pipeline.collection.count()
            st.metric("Indexed Quotes", db_count)
            
            if db_count == 0 and df is not None:
                if st.button("🗂️ Initialize Database"):
                    initialize_database(rag_pipeline, df)
                    st.rerun()
        else:
            st.error("RAG pipeline not available")
    
    # Search execution
    if query and rag_pipeline:
        st.markdown("---")
        st.header("🎯 Search Results")
        
        # Ensure database is initialized
        if rag_pipeline.collection.count() == 0 and df is not None:
            initialize_database(rag_pipeline, df)
        
        # Perform search
        with st.spinner("Searching for relevant quotes..."):
            try:
                results = rag_pipeline.search(query, top_k=top_k)
                
                # Display results
                if results['retrieved_quotes']:
                    # LLM Response
                    st.subheader("🤖 AI Response")
                    st.write(results['response'])
                    
                    if show_raw_response:
                        with st.expander("Raw LLM Response"):
                            st.code(results['response'])
                    
                    # Retrieved quotes
                    st.subheader(f"📋 Retrieved Quotes ({len(results['retrieved_quotes'])})")
                    
                    # Display quotes in tabs
                    if len(results['retrieved_quotes']) > 3:
                        # Use tabs for many results
                        tabs = st.tabs([f"Quote {i+1}" for i in range(len(results['retrieved_quotes']))])
                        for i, tab in enumerate(tabs):
                            with tab:
                                display_quote_card(results['retrieved_quotes'][i], i+1)
                    else:
                        # Display all quotes directly
                        for i, quote_data in enumerate(results['retrieved_quotes']):
                            display_quote_card(quote_data, i+1)
                    
                    # Visualizations
                    if show_similarity_chart or show_tags_chart:
                        st.subheader("📊 Analysis")
                        
                        viz_col1, viz_col2 = st.columns(2)
                        
                        with viz_col1:
                            if show_similarity_chart:
                                sim_chart = create_similarity_chart(results['retrieved_quotes'])
                                if sim_chart:
                                    st.plotly_chart(sim_chart, use_container_width=True)
                        
                        with viz_col2:
                            if show_tags_chart:
                                tags_chart = create_tags_distribution(results['retrieved_quotes'])
                                if tags_chart:
                                    st.plotly_chart(tags_chart, use_container_width=True)
                    
                    # Export functionality
                    st.subheader("💾 Export Results")
                    
                    export_col1, export_col2 = st.columns(2)
                    
                    with export_col1:
                        # JSON export
                        json_data = export_results_to_json(query, results)
                        st.download_button(
                            label="📄 Download as JSON",
                            data=json_data,
                            file_name=f"quote_search_{int(time.time())}.json",
                            mime="application/json"
                        )
                    
                    with export_col2:
                        # CSV export
                        quotes_df = pd.DataFrame(results['retrieved_quotes'])
                        csv_data = quotes_df.to_csv(index=False)
                        st.download_button(
                            label="📊 Download as CSV",
                            data=csv_data,
                            file_name=f"quote_search_{int(time.time())}.csv",
                            mime="text/csv"
                        )
                
                else:
                    st.warning("No relevant quotes found for your query. Try rephrasing or using different keywords.")
                    
            except Exception as e:
                st.error(f"Error during search: {e}")
                logger.error(f"Search error: {e}")
    
    elif query and not rag_pipeline:
        st.error("RAG pipeline is not available. Please check the system configuration.")
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666;'>
        <p>Built with Streamlit • Powered by Sentence Transformers & Mistral • Data from Abirate/english_quotes</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
