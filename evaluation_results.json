{"retrieval_metrics": {"retrieval_accuracy": 0.2, "author_accuracy": 0.8666666666666667, "average_similarity": 0.6455525437990824, "total_queries": 30}, "quality_metrics": {"average_response_length": 110.25, "average_relevance_score": 0.95, "responses_evaluated": 20}, "evaluation_data": [{"question": "Show me quotes by <PERSON>.", "ground_truth_quote": "Intelligence plus character-that is the goal of true education.", "ground_truth_author": "<PERSON> Jr.", "query_type": "author"}, {"question": "What did <PERSON> say about life?", "ground_truth_quote": "Intelligence plus character-that is the goal of true education.", "ground_truth_author": "<PERSON> Jr.", "query_type": "author_topic"}, {"question": "Show me quotes by <PERSON>,", "ground_truth_quote": "Love all, trust a few, do wrong to none.", "ground_truth_author": "<PERSON>,", "query_type": "author"}, {"question": "What did <PERSON>, say about life?", "ground_truth_quote": "Love all, trust a few, do wrong to none.", "ground_truth_author": "<PERSON>,", "query_type": "author_topic"}, {"question": "Show me quotes by <PERSON> BrontÃ,", "ground_truth_quote": "If he loved with all the powers of his puny being, he couldn't love as much in eighty years as I could in a day.", "ground_truth_author": "<PERSON> BrontÃ,", "query_type": "author"}, {"question": "What did <PERSON> BrontÃ, say about life?", "ground_truth_quote": "If he loved with all the powers of his puny being, he couldn't love as much in eighty years as I could in a day.", "ground_truth_author": "<PERSON> BrontÃ,", "query_type": "author_topic"}, {"question": "Show me quotes by <PERSON>", "ground_truth_quote": "Whatever the cost of our libraries, the price is cheap compared to that of an ignorant nation.", "ground_truth_author": "<PERSON>", "query_type": "author"}, {"question": "What did <PERSON> say about life?", "ground_truth_quote": "Whatever the cost of our libraries, the price is cheap compared to that of an ignorant nation.", "ground_truth_author": "<PERSON>", "query_type": "author_topic"}, {"question": "Show me quotes by <PERSON>,", "ground_truth_quote": "That's why when major badasses greet each other in movies, they don't say anything, they just nod. The nod means, 'I' am a badass, and I recognize that you, too, are a badass,' but they don't say anything because they're <PERSON> and <PERSON><PERSON><PERSON> and it would mess up their vibe to explain.", "ground_truth_author": "<PERSON>,", "query_type": "author"}, {"question": "What did <PERSON>, say about life?", "ground_truth_quote": "That's why when major badasses greet each other in movies, they don't say anything, they just nod. The nod means, 'I' am a badass, and I recognize that you, too, are a badass,' but they don't say anything because they're <PERSON> and <PERSON><PERSON><PERSON> and it would mess up their vibe to explain.", "ground_truth_author": "<PERSON>,", "query_type": "author_topic"}]}