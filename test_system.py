"""
Test script to verify the RAG Quote Retrieval System
"""

import sys
import logging
import traceback
from typing import Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all required modules can be imported"""
    logger.info("Testing imports...")
    
    try:
        import torch
        logger.info(f"✓ PyTorch version: {torch.__version__}")
        
        import transformers
        logger.info(f"✓ Transformers version: {transformers.__version__}")
        
        import sentence_transformers
        logger.info(f"✓ Sentence Transformers available")
        
        import chromadb
        logger.info(f"✓ ChromaDB available")
        
        import streamlit
        logger.info(f"✓ Streamlit version: {streamlit.__version__}")
        
        import pandas as pd
        logger.info(f"✓ Pandas version: {pd.__version__}")
        
        # Test our modules
        from data_preparation import QuoteDataProcessor
        from model_training import QuoteEmbeddingTrainer
        from rag_pipeline import QuoteRAGPipeline
        from evaluation import QuoteRAGEvaluator
        
        logger.info("✓ All custom modules imported successfully")
        return True
        
    except ImportError as e:
        logger.error(f"✗ Import error: {e}")
        return False

def test_data_preparation():
    """Test data preparation functionality"""
    logger.info("Testing data preparation...")
    
    try:
        from data_preparation import QuoteDataProcessor
        
        processor = QuoteDataProcessor()
        
        # Test basic functionality without downloading
        test_data = {
            'quote': ['Test quote 1', 'Test quote 2'],
            'author': ['Author 1', 'Author 2'],
            'tags': ['["tag1", "tag2"]', '["tag3"]']
        }
        
        import pandas as pd
        test_df = pd.DataFrame(test_data)
        
        # Test preprocessing
        processed_df = processor.preprocess_data(test_df)
        
        assert len(processed_df) == 2
        assert 'quote_cleaned' in processed_df.columns
        assert 'combined_text' in processed_df.columns
        
        logger.info("✓ Data preparation test passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Data preparation test failed: {e}")
        return False

def test_model_components():
    """Test model components"""
    logger.info("Testing model components...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        # Test loading base model
        model = SentenceTransformer("all-MiniLM-L6-v2")
        
        # Test encoding
        test_texts = ["This is a test sentence", "Another test sentence"]
        embeddings = model.encode(test_texts)
        
        assert embeddings.shape[0] == 2
        assert embeddings.shape[1] > 0
        
        logger.info("✓ Model components test passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Model components test failed: {e}")
        return False

def test_vector_database():
    """Test vector database functionality"""
    logger.info("Testing vector database...")
    
    try:
        import chromadb
        import tempfile
        import os
        
        # Create temporary database
        with tempfile.TemporaryDirectory() as temp_dir:
            client = chromadb.PersistentClient(path=temp_dir)
            collection = client.get_or_create_collection("test_collection")
            
            # Test adding documents
            collection.add(
                documents=["Test document 1", "Test document 2"],
                metadatas=[{"author": "Test Author 1"}, {"author": "Test Author 2"}],
                ids=["doc1", "doc2"]
            )
            
            # Test querying
            results = collection.query(
                query_texts=["Test document"],
                n_results=1
            )
            
            assert len(results['documents'][0]) == 1
            
        logger.info("✓ Vector database test passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Vector database test failed: {e}")
        return False

def test_system_requirements():
    """Test system requirements"""
    logger.info("Testing system requirements...")
    
    try:
        import psutil
        
        # Check memory
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        
        logger.info(f"Total RAM: {memory_gb:.1f} GB")
        
        if memory_gb < 8:
            logger.warning("⚠ Less than 8GB RAM detected. System may be slow.")
        else:
            logger.info("✓ Sufficient RAM available")
        
        # Check disk space
        disk = psutil.disk_usage('.')
        disk_gb = disk.free / (1024**3)
        
        logger.info(f"Free disk space: {disk_gb:.1f} GB")
        
        if disk_gb < 10:
            logger.warning("⚠ Less than 10GB free disk space. May not be sufficient.")
        else:
            logger.info("✓ Sufficient disk space available")
        
        # Check CUDA availability
        import torch
        if torch.cuda.is_available():
            logger.info(f"✓ CUDA available: {torch.cuda.get_device_name()}")
        else:
            logger.info("ℹ CUDA not available. Will use CPU (slower).")
        
        return True
        
    except ImportError:
        logger.warning("psutil not available, skipping system checks")
        return True
    except Exception as e:
        logger.error(f"✗ System requirements test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    logger.info("="*50)
    logger.info("RAG QUOTE RETRIEVAL SYSTEM - SYSTEM TEST")
    logger.info("="*50)
    
    tests = [
        ("Import Test", test_imports),
        ("Data Preparation Test", test_data_preparation),
        ("Model Components Test", test_model_components),
        ("Vector Database Test", test_vector_database),
        ("System Requirements Test", test_system_requirements),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"✗ {test_name} failed with exception: {e}")
            logger.error(traceback.format_exc())
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASSED" if result else "✗ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! System is ready to use.")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the errors above.")
        return False

def main():
    """Main function"""
    success = run_all_tests()
    
    if success:
        logger.info("\n" + "="*50)
        logger.info("NEXT STEPS")
        logger.info("="*50)
        logger.info("1. Run 'python main.py' to execute the full pipeline")
        logger.info("2. Or run 'python main.py --step app' to launch the Streamlit app directly")
        logger.info("3. Or run individual components:")
        logger.info("   - python data_preparation.py")
        logger.info("   - python model_training.py")
        logger.info("   - python rag_pipeline.py")
        logger.info("   - python evaluation.py")
        logger.info("   - streamlit run app.py")
        
        sys.exit(0)
    else:
        logger.error("Please fix the issues above before proceeding.")
        sys.exit(1)

if __name__ == "__main__":
    main()
