"""
RAG Pipeline Module for Quote Retrieval System
Implements Retrieval Augmented Generation using ChromaDB and Mistral
"""

import pandas as pd
import numpy as np
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
import torch
import os
import json
from typing import List, Dict, Any, Tuple
import logging
import re

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QuoteRAGPipeline:
    def __init__(self, 
                 embedding_model_path: str = "./models/fine_tuned_quotes_model",
                 llm_model_name: str = "mistralai/Mistral-7B-Instruct-v0.2",
                 db_path: str = "./chroma_db",
                 use_quantization: bool = True):
        """
        Initialize the RAG pipeline
        
        Args:
            embedding_model_path: Path to fine-tuned embedding model
            llm_model_name: Name of the LLM model
            db_path: Path to ChromaDB database
            use_quantization: Whether to use 4-bit quantization for LLM
        """
        self.db_path = db_path
        self.use_quantization = use_quantization
        
        # Initialize embedding model
        self.load_embedding_model(embedding_model_path)
        
        # Initialize LLM
        self.load_llm(llm_model_name)
        
        # Initialize ChromaDB
        self.init_vector_db()
        
    def load_embedding_model(self, model_path: str):
        """Load the fine-tuned embedding model"""
        try:
            if os.path.exists(model_path):
                self.embedding_model = SentenceTransformer(model_path)
                logger.info(f"Loaded fine-tuned embedding model from {model_path}")
            else:
                logger.warning(f"Fine-tuned model not found at {model_path}, using base model")
                self.embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
        except Exception as e:
            logger.error(f"Error loading embedding model: {e}")
            self.embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
    
    def load_llm(self, model_name: str):
        """Load the LLM model with optional quantization"""
        try:
            logger.info(f"Loading LLM: {model_name}")
            
            # Configure quantization if enabled
            if self.use_quantization:
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_quant_type="nf4",
                    bnb_4bit_use_double_quant=True,
                )
            else:
                quantization_config = None
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model
            self.llm = AutoModelForCausalLM.from_pretrained(
                model_name,
                quantization_config=quantization_config,
                device_map="auto" if torch.cuda.is_available() else None,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                trust_remote_code=True
            )
            
            logger.info("LLM loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading LLM: {e}")
            logger.info("Falling back to a simpler response generation")
            self.llm = None
            self.tokenizer = None
    
    def init_vector_db(self):
        """Initialize ChromaDB"""
        try:
            # Create ChromaDB client
            self.chroma_client = chromadb.PersistentClient(path=self.db_path)
            
            # Get or create collection
            self.collection = self.chroma_client.get_or_create_collection(
                name="quotes_collection",
                metadata={"hnsw:space": "cosine"}
            )
            
            logger.info("ChromaDB initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing ChromaDB: {e}")
            raise
    
    def index_quotes(self, df: pd.DataFrame, batch_size: int = 100):
        """
        Index quotes in the vector database
        
        Args:
            df: Processed quotes DataFrame
            batch_size: Batch size for indexing
        """
        logger.info("Indexing quotes in vector database...")
        
        # Check if collection already has data
        if self.collection.count() > 0:
            logger.info(f"Collection already contains {self.collection.count()} documents")
            return
        
        # Prepare data for indexing
        documents = []
        metadatas = []
        ids = []
        
        for idx, row in df.iterrows():
            # Create document text
            doc_text = row['combined_text']
            documents.append(doc_text)
            
            # Create metadata
            metadata = {
                'quote': row['quote_cleaned'],
                'author': row['author_cleaned'],
                'tags': row['tags_str'],
                'original_index': int(row['id'])
            }
            metadatas.append(metadata)
            
            # Create ID
            ids.append(f"quote_{row['id']}")
        
        # Index in batches
        for i in range(0, len(documents), batch_size):
            batch_docs = documents[i:i+batch_size]
            batch_metadata = metadatas[i:i+batch_size]
            batch_ids = ids[i:i+batch_size]
            
            # Generate embeddings
            embeddings = self.embedding_model.encode(batch_docs).tolist()
            
            # Add to collection
            self.collection.add(
                documents=batch_docs,
                metadatas=batch_metadata,
                embeddings=embeddings,
                ids=batch_ids
            )
            
            if (i + batch_size) % 500 == 0:
                logger.info(f"Indexed {min(i + batch_size, len(documents))} documents")
        
        logger.info(f"Indexing completed. Total documents: {len(documents)}")
    
    def retrieve_quotes(self, query: str, top_k: int = 5) -> List[Dict]:
        """
        Retrieve relevant quotes for a query
        
        Args:
            query: Search query
            top_k: Number of top results to return
            
        Returns:
            List of retrieved quote dictionaries
        """
        # Generate query embedding
        query_embedding = self.embedding_model.encode([query]).tolist()[0]
        
        # Search in vector database
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=top_k,
            include=['documents', 'metadatas', 'distances']
        )
        
        # Format results
        retrieved_quotes = []
        for i in range(len(results['documents'][0])):
            quote_data = {
                'quote': results['metadatas'][0][i]['quote'],
                'author': results['metadatas'][0][i]['author'],
                'tags': results['metadatas'][0][i]['tags'],
                'similarity_score': 1 - results['distances'][0][i],  # Convert distance to similarity
                'document': results['documents'][0][i]
            }
            retrieved_quotes.append(quote_data)
        
        return retrieved_quotes
    
    def generate_response(self, query: str, retrieved_quotes: List[Dict]) -> str:
        """
        Generate response using LLM
        
        Args:
            query: User query
            retrieved_quotes: Retrieved quote data
            
        Returns:
            Generated response
        """
        if self.llm is None or self.tokenizer is None:
            # Fallback response generation
            return self.generate_simple_response(query, retrieved_quotes)
        
        # Create context from retrieved quotes
        context = self.create_context(retrieved_quotes)
        
        # Create prompt
        prompt = self.create_prompt(query, context)
        
        try:
            # Tokenize input
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
            
            # Move to device
            if torch.cuda.is_available():
                inputs = {k: v.to(self.llm.device) for k, v in inputs.items()}
            
            # Generate response
            with torch.no_grad():
                outputs = self.llm.generate(
                    **inputs,
                    max_new_tokens=512,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract only the generated part
            response = response[len(prompt):].strip()
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating response with LLM: {e}")
            return self.generate_simple_response(query, retrieved_quotes)
    
    def generate_simple_response(self, query: str, retrieved_quotes: List[Dict]) -> str:
        """
        Generate a simple response without LLM
        
        Args:
            query: User query
            retrieved_quotes: Retrieved quote data
            
        Returns:
            Simple formatted response
        """
        if not retrieved_quotes:
            return "I couldn't find any relevant quotes for your query."
        
        response = f"Here are the most relevant quotes for '{query}':\n\n"
        
        for i, quote_data in enumerate(retrieved_quotes[:3], 1):
            response += f"{i}. \"{quote_data['quote']}\"\n"
            response += f"   - Author: {quote_data['author']}\n"
            if quote_data['tags']:
                response += f"   - Tags: {quote_data['tags']}\n"
            response += f"   - Relevance: {quote_data['similarity_score']:.2f}\n\n"
        
        return response
    
    def create_context(self, retrieved_quotes: List[Dict]) -> str:
        """Create context string from retrieved quotes"""
        context = "Relevant quotes:\n"
        for i, quote_data in enumerate(retrieved_quotes, 1):
            context += f"{i}. \"{quote_data['quote']}\" - {quote_data['author']}"
            if quote_data['tags']:
                context += f" (Tags: {quote_data['tags']})"
            context += "\n"
        return context
    
    def create_prompt(self, query: str, context: str) -> str:
        """Create prompt for LLM"""
        prompt = f"""<s>[INST] You are a helpful assistant that provides information about quotes. Based on the following relevant quotes, answer the user's question.

{context}

User Question: {query}

Please provide a helpful response that includes relevant quotes and their authors. Format your response clearly and include any relevant tags or themes. [/INST]"""
        
        return prompt
    
    def search(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """
        Main search function that combines retrieval and generation
        
        Args:
            query: Search query
            top_k: Number of quotes to retrieve
            
        Returns:
            Dictionary with search results and response
        """
        # Retrieve relevant quotes
        retrieved_quotes = self.retrieve_quotes(query, top_k)
        
        # Generate response
        response = self.generate_response(query, retrieved_quotes)
        
        return {
            'query': query,
            'response': response,
            'retrieved_quotes': retrieved_quotes,
            'num_results': len(retrieved_quotes)
        }

def main():
    """Main function to test RAG pipeline"""
    from data_preparation import QuoteDataProcessor
    
    # Load processed data
    processor = QuoteDataProcessor()
    try:
        df = processor.load_processed_data()
    except FileNotFoundError:
        logger.error("Processed data not found. Please run data_preparation.py first.")
        return
    
    # Initialize RAG pipeline
    rag = QuoteRAGPipeline()
    
    # Index quotes
    rag.index_quotes(df)
    
    # Test queries
    test_queries = [
        "Quotes about hope by Oscar Wilde",
        "Motivational quotes about success",
        "Quotes about love and life"
    ]
    
    for query in test_queries:
        logger.info(f"\nTesting query: {query}")
        result = rag.search(query)
        print(f"Response: {result['response']}")
        print("-" * 50)

if __name__ == "__main__":
    main()
