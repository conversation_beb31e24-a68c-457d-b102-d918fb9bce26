"""
Evaluation Module for Quote Retrieval System
Evaluates the RAG pipeline using RAGAS framework
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any
import logging
import json
import os
from datasets import Dataset
import asyncio

# RAGAS imports
try:
    from ragas import evaluate
    from ragas.metrics import (
        faithfulness,
        answer_relevancy,
        context_precision,
        context_recall,
        context_relevancy
    )
    RAGAS_AVAILABLE = True
except ImportError:
    RAGAS_AVAILABLE = False
    logging.warning("RAGAS not available. Using custom evaluation metrics.")

from rag_pipeline import QuoteRAGPipeline

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QuoteRAGEvaluator:
    def __init__(self, rag_pipeline: QuoteRAGPipeline):
        """
        Initialize the evaluator
        
        Args:
            rag_pipeline: Initialized RAG pipeline
        """
        self.rag_pipeline = rag_pipeline
        self.evaluation_results = {}
        
    def create_evaluation_dataset(self, df: pd.DataFrame, num_samples: int = 100) -> List[Dict]:
        """
        Create evaluation dataset with ground truth
        
        Args:
            df: Processed quotes DataFrame
            num_samples: Number of evaluation samples
            
        Returns:
            List of evaluation examples
        """
        logger.info("Creating evaluation dataset...")
        
        evaluation_data = []
        
        # Sample quotes for evaluation
        sample_df = df.sample(min(num_samples, len(df)), random_state=42)
        
        for idx, row in sample_df.iterrows():
            quote = row['quote_cleaned']
            author = row['author_cleaned']
            tags = row['tags_str']
            
            if len(quote) < 20 or author == "Unknown":
                continue
            
            # Create different types of queries
            queries = []
            
            # Author-based queries
            if author:
                queries.append({
                    'question': f"Show me quotes by {author}",
                    'ground_truth_quote': quote,
                    'ground_truth_author': author,
                    'query_type': 'author'
                })
                
                queries.append({
                    'question': f"What did {author} say about life?",
                    'ground_truth_quote': quote,
                    'ground_truth_author': author,
                    'query_type': 'author_topic'
                })
            
            # Tag-based queries
            if tags:
                tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
                if tag_list:
                    main_tag = tag_list[0]
                    queries.append({
                        'question': f"Quotes about {main_tag}",
                        'ground_truth_quote': quote,
                        'ground_truth_author': author,
                        'query_type': 'tag'
                    })
            
            # Content-based queries
            # Extract key words from quote
            words = quote.lower().split()
            key_words = [w for w in words if len(w) > 4 and w.isalpha()]
            if key_words:
                key_word = key_words[0]
                queries.append({
                    'question': f"Quotes containing the word {key_word}",
                    'ground_truth_quote': quote,
                    'ground_truth_author': author,
                    'query_type': 'content'
                })
            
            evaluation_data.extend(queries[:2])  # Limit to 2 queries per quote
            
            if len(evaluation_data) >= num_samples:
                break
        
        logger.info(f"Created {len(evaluation_data)} evaluation examples")
        return evaluation_data
    
    def evaluate_retrieval_accuracy(self, evaluation_data: List[Dict], top_k: int = 5) -> Dict[str, float]:
        """
        Evaluate retrieval accuracy
        
        Args:
            evaluation_data: List of evaluation examples
            top_k: Number of top results to consider
            
        Returns:
            Dictionary with accuracy metrics
        """
        logger.info("Evaluating retrieval accuracy...")
        
        correct_retrievals = 0
        author_matches = 0
        total_queries = len(evaluation_data)
        
        retrieval_scores = []
        
        for example in evaluation_data:
            query = example['question']
            ground_truth_quote = example['ground_truth_quote']
            ground_truth_author = example['ground_truth_author']
            
            # Get retrieval results
            retrieved_quotes = self.rag_pipeline.retrieve_quotes(query, top_k)
            
            # Check if ground truth quote is in top-k results
            quote_found = False
            author_found = False
            best_similarity = 0
            
            for retrieved in retrieved_quotes:
                # Check quote similarity (exact or high similarity)
                if (ground_truth_quote.lower() in retrieved['quote'].lower() or 
                    retrieved['quote'].lower() in ground_truth_quote.lower()):
                    quote_found = True
                
                # Check author match
                if ground_truth_author.lower() in retrieved['author'].lower():
                    author_found = True
                
                # Track best similarity score
                best_similarity = max(best_similarity, retrieved['similarity_score'])
            
            if quote_found:
                correct_retrievals += 1
            if author_found:
                author_matches += 1
            
            retrieval_scores.append(best_similarity)
        
        accuracy_metrics = {
            'retrieval_accuracy': correct_retrievals / total_queries,
            'author_accuracy': author_matches / total_queries,
            'average_similarity': np.mean(retrieval_scores),
            'total_queries': total_queries
        }
        
        return accuracy_metrics
    
    def evaluate_response_quality(self, evaluation_data: List[Dict], sample_size: int = 20) -> Dict[str, float]:
        """
        Evaluate response quality using simple metrics
        
        Args:
            evaluation_data: List of evaluation examples
            sample_size: Number of samples to evaluate
            
        Returns:
            Dictionary with quality metrics
        """
        logger.info("Evaluating response quality...")
        
        sample_data = evaluation_data[:sample_size]
        
        response_lengths = []
        relevance_scores = []
        
        for example in sample_data:
            query = example['question']
            ground_truth_author = example['ground_truth_author']
            
            # Get full search result
            result = self.rag_pipeline.search(query, top_k=3)
            response = result['response']
            
            # Basic quality metrics
            response_lengths.append(len(response.split()))
            
            # Simple relevance check
            relevance_score = 0
            if ground_truth_author.lower() in response.lower():
                relevance_score += 0.5
            if any(quote['author'].lower() == ground_truth_author.lower() 
                   for quote in result['retrieved_quotes']):
                relevance_score += 0.5
            
            relevance_scores.append(relevance_score)
        
        quality_metrics = {
            'average_response_length': np.mean(response_lengths),
            'average_relevance_score': np.mean(relevance_scores),
            'responses_evaluated': len(sample_data)
        }
        
        return quality_metrics
    
    def evaluate_with_ragas(self, evaluation_data: List[Dict], sample_size: int = 10) -> Dict[str, float]:
        """
        Evaluate using RAGAS framework (if available)
        
        Args:
            evaluation_data: List of evaluation examples
            sample_size: Number of samples to evaluate
            
        Returns:
            Dictionary with RAGAS metrics
        """
        if not RAGAS_AVAILABLE:
            logger.warning("RAGAS not available, skipping RAGAS evaluation")
            return {}
        
        logger.info("Evaluating with RAGAS...")
        
        # Prepare data for RAGAS
        sample_data = evaluation_data[:sample_size]
        
        questions = []
        answers = []
        contexts = []
        ground_truths = []
        
        for example in sample_data:
            query = example['question']
            ground_truth = example['ground_truth_quote']
            
            # Get search result
            result = self.rag_pipeline.search(query, top_k=3)
            
            questions.append(query)
            answers.append(result['response'])
            
            # Create context from retrieved quotes
            context = []
            for quote_data in result['retrieved_quotes']:
                context.append(f"\"{quote_data['quote']}\" - {quote_data['author']}")
            contexts.append(context)
            
            ground_truths.append(ground_truth)
        
        # Create dataset for RAGAS
        dataset = Dataset.from_dict({
            'question': questions,
            'answer': answers,
            'contexts': contexts,
            'ground_truth': ground_truths
        })
        
        try:
            # Run RAGAS evaluation
            result = evaluate(
                dataset=dataset,
                metrics=[
                    context_precision,
                    context_recall,
                    context_relevancy,
                    answer_relevancy,
                    faithfulness
                ]
            )
            
            ragas_metrics = {
                'context_precision': result['context_precision'],
                'context_recall': result['context_recall'],
                'context_relevancy': result['context_relevancy'],
                'answer_relevancy': result['answer_relevancy'],
                'faithfulness': result['faithfulness']
            }
            
            return ragas_metrics
            
        except Exception as e:
            logger.error(f"Error running RAGAS evaluation: {e}")
            return {}
    
    def run_comprehensive_evaluation(self, df: pd.DataFrame, num_samples: int = 50) -> Dict[str, Any]:
        """
        Run comprehensive evaluation
        
        Args:
            df: Processed quotes DataFrame
            num_samples: Number of evaluation samples
            
        Returns:
            Complete evaluation results
        """
        logger.info("Running comprehensive evaluation...")
        
        # Create evaluation dataset
        evaluation_data = self.create_evaluation_dataset(df, num_samples)
        
        # Run different evaluations
        results = {}
        
        # Retrieval accuracy
        results['retrieval_metrics'] = self.evaluate_retrieval_accuracy(evaluation_data)
        
        # Response quality
        results['quality_metrics'] = self.evaluate_response_quality(evaluation_data, min(20, len(evaluation_data)))
        
        # RAGAS evaluation (if available)
        if RAGAS_AVAILABLE:
            results['ragas_metrics'] = self.evaluate_with_ragas(evaluation_data, min(10, len(evaluation_data)))
        
        # Store evaluation data for analysis
        results['evaluation_data'] = evaluation_data[:10]  # Store sample for inspection
        
        self.evaluation_results = results
        return results
    
    def save_evaluation_results(self, filepath: str = "./evaluation_results.json"):
        """
        Save evaluation results to file
        
        Args:
            filepath: Path to save results
        """
        with open(filepath, 'w') as f:
            json.dump(self.evaluation_results, f, indent=2, default=str)
        logger.info(f"Evaluation results saved to {filepath}")
    
    def print_evaluation_summary(self):
        """Print a summary of evaluation results"""
        if not self.evaluation_results:
            logger.warning("No evaluation results available")
            return
        
        print("\n" + "="*50)
        print("EVALUATION SUMMARY")
        print("="*50)
        
        # Retrieval metrics
        if 'retrieval_metrics' in self.evaluation_results:
            metrics = self.evaluation_results['retrieval_metrics']
            print(f"\nRetrieval Accuracy:")
            print(f"  Quote Retrieval Accuracy: {metrics['retrieval_accuracy']:.3f}")
            print(f"  Author Accuracy: {metrics['author_accuracy']:.3f}")
            print(f"  Average Similarity Score: {metrics['average_similarity']:.3f}")
            print(f"  Total Queries Evaluated: {metrics['total_queries']}")
        
        # Quality metrics
        if 'quality_metrics' in self.evaluation_results:
            metrics = self.evaluation_results['quality_metrics']
            print(f"\nResponse Quality:")
            print(f"  Average Response Length: {metrics['average_response_length']:.1f} words")
            print(f"  Average Relevance Score: {metrics['average_relevance_score']:.3f}")
            print(f"  Responses Evaluated: {metrics['responses_evaluated']}")
        
        # RAGAS metrics
        if 'ragas_metrics' in self.evaluation_results and self.evaluation_results['ragas_metrics']:
            metrics = self.evaluation_results['ragas_metrics']
            print(f"\nRAGAS Metrics:")
            for metric_name, value in metrics.items():
                print(f"  {metric_name}: {value:.3f}")

def main():
    """Main function to run evaluation"""
    from data_preparation import QuoteDataProcessor
    
    # Load processed data
    processor = QuoteDataProcessor()
    try:
        df = processor.load_processed_data()
    except FileNotFoundError:
        logger.error("Processed data not found. Please run data_preparation.py first.")
        return
    
    # Initialize RAG pipeline
    rag = QuoteRAGPipeline()
    
    # Check if quotes are indexed
    if rag.collection.count() == 0:
        logger.info("Indexing quotes...")
        rag.index_quotes(df)
    
    # Initialize evaluator
    evaluator = QuoteRAGEvaluator(rag)
    
    # Run evaluation
    results = evaluator.run_comprehensive_evaluation(df, num_samples=30)
    
    # Print summary
    evaluator.print_evaluation_summary()
    
    # Save results
    evaluator.save_evaluation_results()

if __name__ == "__main__":
    main()
