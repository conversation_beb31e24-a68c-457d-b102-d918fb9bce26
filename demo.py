"""
Demo script for RAG Quote Retrieval System
Demonstrates the system capabilities with example queries
"""

import logging
from rag_pipeline import QuoteRAGPipeline
from data_preparation import QuoteDataProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demo_search_functionality():
    """Demonstrate the search functionality"""
    
    print("="*60)
    print("RAG QUOTE RETRIEVAL SYSTEM - DEMO")
    print("="*60)
    
    # Initialize the system
    print("\n🔧 Initializing RAG Pipeline...")
    rag = QuoteRAGPipeline()
    
    # Load dataset info
    processor = QuoteDataProcessor()
    df = processor.load_processed_data()
    
    print(f"📊 Dataset loaded: {len(df)} quotes indexed")
    print(f"🗄️ Vector database: {rag.collection.count()} documents")
    
    # Demo queries
    demo_queries = [
        {
            "query": "Quotes about hope by <PERSON>",
            "description": "Author-specific search with topic"
        },
        {
            "query": "Motivational quotes about success",
            "description": "Topic-based search"
        },
        {
            "query": "Quotes about love and life",
            "description": "Multi-topic search"
        },
        {
            "query": "Inspirational quotes by women authors",
            "description": "Gender-specific author search"
        },
        {
            "query": "Quotes tagged with accomplishment",
            "description": "Tag-based search"
        }
    ]
    
    print("\n🎯 Running Demo Queries...")
    print("-" * 60)
    
    for i, demo in enumerate(demo_queries, 1):
        print(f"\n{i}. {demo['description']}")
        print(f"Query: '{demo['query']}'")
        print("-" * 40)
        
        try:
            # Perform search
            result = rag.search(demo['query'], top_k=3)
            
            # Display results
            print(f"📝 AI Response:")
            print(f"{result['response'][:200]}...")
            
            print(f"\n📋 Top {len(result['retrieved_quotes'])} Retrieved Quotes:")
            
            for j, quote_data in enumerate(result['retrieved_quotes'], 1):
                print(f"\n  {j}. \"{quote_data['quote'][:100]}...\"")
                print(f"     Author: {quote_data['author']}")
                print(f"     Tags: {quote_data['tags'][:50]}...")
                print(f"     Similarity: {quote_data['similarity_score']:.3f}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("\n" + "="*60)
    
    print("\n🎉 Demo completed successfully!")
    print("\n💡 To explore more:")
    print("   - Run 'streamlit run app.py' for the web interface")
    print("   - Visit http://localhost:8501 in your browser")
    print("   - Try your own custom queries!")

def interactive_demo():
    """Interactive demo mode"""
    
    print("\n🎮 Interactive Demo Mode")
    print("Type your queries or 'quit' to exit")
    print("-" * 40)
    
    rag = QuoteRAGPipeline()
    
    while True:
        try:
            query = input("\n🔍 Enter your query: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not query:
                continue
            
            print(f"\n🔄 Searching for: '{query}'")
            
            result = rag.search(query, top_k=3)
            
            print(f"\n📝 Response:")
            print(result['response'])
            
            print(f"\n📊 Found {len(result['retrieved_quotes'])} relevant quotes:")
            
            for i, quote_data in enumerate(result['retrieved_quotes'], 1):
                print(f"\n{i}. \"{quote_data['quote']}\"")
                print(f"   - {quote_data['author']} (Score: {quote_data['similarity_score']:.3f})")
            
        except KeyboardInterrupt:
            print("\n👋 Demo interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main demo function"""
    
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_demo()
    else:
        demo_search_functionality()

if __name__ == "__main__":
    main()
