"""
Data Preparation Module for Quote Retrieval System
Downloads and preprocesses the Abirate/english_quotes dataset
"""

import pandas as pd
import numpy as np
from datasets import load_dataset
import re
import json
import os
from typing import Dict, List, Tuple
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QuoteDataProcessor:
    def __init__(self, cache_dir: str = "./data"):
        """
        Initialize the data processor
        
        Args:
            cache_dir: Directory to cache the dataset
        """
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
    def download_dataset(self) -> pd.DataFrame:
        """
        Download the english_quotes dataset from HuggingFace
        
        Returns:
            DataFrame containing the quotes data
        """
        logger.info("Downloading english_quotes dataset...")
        
        try:
            # Load dataset from HuggingFace
            dataset = load_dataset("Abirate/english_quotes", cache_dir=self.cache_dir)
            
            # Convert to pandas DataFrame
            df = pd.DataFrame(dataset['train'])
            
            logger.info(f"Dataset downloaded successfully. Shape: {df.shape}")
            logger.info(f"Columns: {df.columns.tolist()}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error downloading dataset: {e}")
            raise
    
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize text
        
        Args:
            text: Raw text to clean
            
        Returns:
            Cleaned text
        """
        if pd.isna(text) or text is None:
            return ""
        
        # Convert to string
        text = str(text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\'\"\-]', '', text)
        
        # Strip leading/trailing whitespace
        text = text.strip()
        
        return text
    
    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Preprocess the quotes dataset
        
        Args:
            df: Raw DataFrame
            
        Returns:
            Preprocessed DataFrame
        """
        logger.info("Preprocessing data...")
        
        # Create a copy
        processed_df = df.copy()
        
        # Handle missing values
        processed_df['quote'] = processed_df['quote'].fillna("")
        processed_df['author'] = processed_df['author'].fillna("Unknown")
        processed_df['tags'] = processed_df['tags'].fillna("[]")
        
        # Clean text fields
        processed_df['quote_cleaned'] = processed_df['quote'].apply(self.clean_text)
        processed_df['author_cleaned'] = processed_df['author'].apply(self.clean_text)
        
        # Process tags
        processed_df['tags_list'] = processed_df['tags'].apply(self.parse_tags)
        processed_df['tags_str'] = processed_df['tags_list'].apply(lambda x: ', '.join(x) if x else "")
        
        # Remove empty quotes
        processed_df = processed_df[processed_df['quote_cleaned'].str.len() > 0]
        
        # Create combined text for embedding
        processed_df['combined_text'] = processed_df.apply(self.create_combined_text, axis=1)
        
        # Add unique ID
        processed_df['id'] = range(len(processed_df))
        
        logger.info(f"Preprocessing complete. Final shape: {processed_df.shape}")
        
        return processed_df
    
    def parse_tags(self, tags_str: str) -> List[str]:
        """
        Parse tags from string format
        
        Args:
            tags_str: String representation of tags
            
        Returns:
            List of tags
        """
        try:
            if pd.isna(tags_str) or tags_str == "":
                return []
            
            # Try to parse as JSON
            if tags_str.startswith('[') and tags_str.endswith(']'):
                tags = json.loads(tags_str)
                return [self.clean_text(tag) for tag in tags if tag]
            else:
                # Split by comma if not JSON format
                tags = [tag.strip() for tag in tags_str.split(',')]
                return [self.clean_text(tag) for tag in tags if tag]
                
        except Exception:
            # Fallback: split by comma
            tags = [tag.strip() for tag in str(tags_str).split(',')]
            return [self.clean_text(tag) for tag in tags if tag]
    
    def create_combined_text(self, row: pd.Series) -> str:
        """
        Create combined text for embedding
        
        Args:
            row: DataFrame row
            
        Returns:
            Combined text string
        """
        quote = row['quote_cleaned']
        author = row['author_cleaned']
        tags = row['tags_str']
        
        combined = f"Quote: {quote}"
        if author and author != "Unknown":
            combined += f" Author: {author}"
        if tags:
            combined += f" Tags: {tags}"
            
        return combined
    
    def get_data_statistics(self, df: pd.DataFrame) -> Dict:
        """
        Get statistics about the dataset
        
        Args:
            df: Processed DataFrame
            
        Returns:
            Dictionary with statistics
        """
        stats = {
            'total_quotes': len(df),
            'unique_authors': df['author_cleaned'].nunique(),
            'avg_quote_length': df['quote_cleaned'].str.len().mean(),
            'total_tags': sum(len(tags) for tags in df['tags_list']),
            'unique_tags': len(set(tag for tags in df['tags_list'] for tag in tags)),
            'quotes_with_tags': sum(1 for tags in df['tags_list'] if tags),
        }
        
        return stats
    
    def save_processed_data(self, df: pd.DataFrame, filename: str = "processed_quotes.csv"):
        """
        Save processed data to file
        
        Args:
            df: Processed DataFrame
            filename: Output filename
        """
        filepath = os.path.join(self.cache_dir, filename)
        df.to_csv(filepath, index=False)
        logger.info(f"Processed data saved to {filepath}")
    
    def load_processed_data(self, filename: str = "processed_quotes.csv") -> pd.DataFrame:
        """
        Load processed data from file
        
        Args:
            filename: Input filename
            
        Returns:
            Processed DataFrame
        """
        filepath = os.path.join(self.cache_dir, filename)
        if os.path.exists(filepath):
            df = pd.read_csv(filepath)
            # Parse tags_list back from string
            df['tags_list'] = df['tags_list'].apply(lambda x: eval(x) if pd.notna(x) else [])
            logger.info(f"Processed data loaded from {filepath}")
            return df
        else:
            raise FileNotFoundError(f"File {filepath} not found")

def main():
    """Main function to run data preparation"""
    processor = QuoteDataProcessor()
    
    # Download and preprocess data
    raw_df = processor.download_dataset()
    processed_df = processor.preprocess_data(raw_df)
    
    # Get statistics
    stats = processor.get_data_statistics(processed_df)
    print("\nDataset Statistics:")
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    # Save processed data
    processor.save_processed_data(processed_df)
    
    # Display sample data
    print("\nSample processed data:")
    print(processed_df[['quote_cleaned', 'author_cleaned', 'tags_str']].head())

if __name__ == "__main__":
    main()
