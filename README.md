# RAG-Based Semantic Quote Retrieval System

A comprehensive semantic quote retrieval system powered by Retrieval Augmented Generation (RAG) using the Abirate/english_quotes dataset. The system includes model fine-tuning, vector database indexing, RAG pipeline implementation, evaluation framework, and an interactive Streamlit web application.

## 🎯 Project Overview

This project implements a complete RAG pipeline for semantic quote retrieval with the following capabilities:
- Fine-tuned sentence embedding model for better semantic understanding
- Vector database for efficient similarity search
- LLM-powered response generation using Mistral-7B
- Comprehensive evaluation using RAGAS framework
- Interactive web interface for user queries

## 🏗️ Architecture

```
User Query → Embedding Model → Vector Search → Context Retrieval → LLM Generation → Response
```

### Components:
1. **Data Preparation**: Downloads and preprocesses the english_quotes dataset
2. **Model Training**: Fine-tunes sentence-transformers for quote embeddings
3. **RAG Pipeline**: Implements retrieval and generation using ChromaDB + Mistral
4. **Evaluation**: Comprehensive evaluation using RAGAS and custom metrics
5. **Streamlit App**: Interactive web interface for quote search

## 📋 Requirements

### System Requirements
- Python 3.8+
- 16GB+ RAM (8GB minimum with quantization)
- ~8GB disk space
- GPU recommended (CUDA-compatible) but not required

### Dependencies
See `requirements.txt` for complete list. Key dependencies:
- `torch>=2.0.0`
- `transformers>=4.30.0`
- `sentence-transformers>=2.2.0`
- `chromadb>=0.4.0`
- `streamlit>=1.25.0`
- `ragas>=0.1.0`

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Create virtual environment
python -m venv quote_rag_env
source quote_rag_env/bin/activate  # On Windows: quote_rag_env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Data Preparation
```bash
python data_preparation.py
```
This will:
- Download the Abirate/english_quotes dataset
- Clean and preprocess the data
- Save processed data to `./data/processed_quotes.csv`

### 3. Model Training (Optional)
```bash
python model_training.py
```
This will:
- Fine-tune a sentence-transformers model on the quotes
- Save the model to `./models/fine_tuned_quotes_model/`

### 4. Run the Streamlit Application
```bash
streamlit run app.py
```
The app will automatically:
- Load the RAG pipeline
- Initialize the vector database
- Provide an interactive interface for quote search

## 📁 Project Structure

```
├── data_preparation.py      # Dataset download and preprocessing
├── model_training.py        # Fine-tuning sentence embedding model
├── rag_pipeline.py         # RAG implementation with ChromaDB + Mistral
├── evaluation.py           # RAGAS evaluation framework
├── app.py                  # Streamlit web application
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── data/                  # Dataset storage
│   └── processed_quotes.csv
├── models/                # Model storage
│   └── fine_tuned_quotes_model/
├── chroma_db/            # Vector database
└── evaluation_results.json  # Evaluation metrics
```

## 🔧 Configuration

### Model Selection
The system uses the following models by default:
- **Embedding Model**: `all-MiniLM-L6-v2` (fine-tuned)
- **LLM**: `mistralai/Mistral-7B-Instruct-v0.2`
- **Vector DB**: ChromaDB with cosine similarity

### Customization
You can modify the models in the respective files:
- Change embedding model in `model_training.py` and `rag_pipeline.py`
- Switch LLM in `rag_pipeline.py` (line 25)
- Adjust vector database settings in `rag_pipeline.py`

## 📊 Evaluation

### Running Evaluation
```bash
python evaluation.py
```

### Metrics Included
1. **Retrieval Accuracy**: Quote and author retrieval precision
2. **Response Quality**: Response length and relevance
3. **RAGAS Metrics**: Context precision, recall, relevancy, answer relevancy, faithfulness

### Sample Results
```
Retrieval Accuracy:
  Quote Retrieval Accuracy: 0.750
  Author Accuracy: 0.820
  Average Similarity Score: 0.685

Response Quality:
  Average Response Length: 45.2 words
  Average Relevance Score: 0.720

RAGAS Metrics:
  context_precision: 0.680
  context_recall: 0.720
  answer_relevancy: 0.750
```

## 🌐 Streamlit Application Features

### Search Interface
- Natural language query input
- Example queries for quick testing
- Configurable number of results

### Results Display
- AI-generated responses
- Retrieved quotes with similarity scores
- Author and tag information
- Interactive visualizations

### Export Options
- JSON format for programmatic use
- CSV format for data analysis
- Downloadable search results

### Example Queries
- "Quotes about insanity attributed to Einstein"
- "Motivational quotes tagged 'accomplishment'"
- "All Oscar Wilde quotes with humor"
- "Quotes about love and life"
- "Inspirational quotes by women authors"

## 🎥 Demo Video

[Link to demo video will be provided]

The demo video includes:
- Code walkthrough of all components
- Live demonstration of the Streamlit app
- Evaluation results discussion
- Performance analysis

## 🔍 Technical Details

### Data Processing
- Handles missing values and malformed data
- Text cleaning and normalization
- Tag parsing from JSON/string formats
- Combined text creation for better embeddings

### Model Fine-tuning
- Creates positive/negative training pairs
- Uses CosineSimilarityLoss for training
- Implements evaluation during training
- Saves best performing model

### RAG Implementation
- ChromaDB for vector storage and retrieval
- Sentence-transformers for embedding generation
- Mistral-7B with 4-bit quantization for efficiency
- Context-aware prompt engineering

### Evaluation Framework
- Custom metrics for retrieval accuracy
- RAGAS integration for comprehensive evaluation
- Ground truth creation from dataset
- Multiple query types (author, tag, content-based)

## 🚧 Challenges and Solutions

### Challenge 1: Memory Requirements
**Problem**: Large language models require significant memory
**Solution**: Implemented 4-bit quantization using BitsAndBytesConfig

### Challenge 2: Evaluation Complexity
**Problem**: Evaluating RAG systems requires multiple metrics
**Solution**: Combined custom metrics with RAGAS framework

### Challenge 3: Response Quality
**Problem**: Ensuring relevant and coherent responses
**Solution**: Fine-tuned embeddings + context-aware prompting

### Challenge 4: Scalability
**Problem**: Handling large datasets efficiently
**Solution**: Batch processing + persistent vector database

## 🔮 Future Improvements

1. **Multi-hop Queries**: Support for complex queries with multiple conditions
2. **Advanced Filtering**: Filter by time period, quote length, etc.
3. **Personalization**: User preference learning
4. **API Integration**: REST API for programmatic access
5. **Performance Optimization**: Caching and query optimization

## 📝 License

This project is for educational and interview purposes only.

## 🤝 Contributing

This is an interview assignment project. For questions or clarifications, please contact the author.

## 📞 Contact

[Your contact information]

---

**Note**: This system is designed for demonstration purposes and may require additional optimization for production use.
